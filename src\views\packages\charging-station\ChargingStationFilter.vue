<template>
  <container class="charging-station-filter">
    <x-header title="筛选条件">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="reset">重置</x-button>
    </x-header>

    <content-view :status="AppStatus.READY">
      <div class="filter-content">
        <!-- 电压 -->
        <div class="filter-section">
          <div class="section-title">电压</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.voltage">
              <van-checkbox name="200-500v">200V-500V</van-checkbox>
              <van-checkbox name="700v+">700V以上</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 充电功率 -->
        <div class="filter-section">
          <div class="section-title">充电功率</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.power">
              <van-checkbox name="15-50kw">15KW-50KW</van-checkbox>
              <van-checkbox name="50-120kw">50KW-120KW</van-checkbox>
              <van-checkbox name="120-300kw">120KW-300KW</van-checkbox>
              <van-checkbox name="300-500kw">300KW-500KW</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 营业时间 -->
        <div class="filter-section">
          <div class="section-title">营业时间</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.businessHoursType">
              <van-checkbox name="TWENTY_FOUR_HOURS">24小时</van-checkbox>
              <van-checkbox name="OPEN">营业中</van-checkbox>
              <van-checkbox name="UNKNOWN">不确定</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 高速模式 -->
        <div class="filter-section">
          <div class="section-title">高速模式</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.highwayMode">
              <van-checkbox name="ON_HIGHWAY">高速路充电桩</van-checkbox>
              <van-checkbox name="NEAR_HIGHWAY">靠近高速路充电桩</van-checkbox>
              <van-checkbox name="NONE">普通充电桩</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 停车场类型 -->
        <div class="filter-section">
          <div class="section-title">停车场类型</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.parkingTypes">
              <van-checkbox name="TRUCK">箱货</van-checkbox>
              <van-checkbox name="BUS">大巴</van-checkbox>
              <van-checkbox name="HEAVY_TRUCK">重卡</van-checkbox>
              <van-checkbox name="GROUND">地上</van-checkbox>
              <van-checkbox name="UNDERGROUND">地下</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 停车费类型 -->
        <div class="filter-section">
          <div class="section-title">停车费类型</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.parkingFeeType">
              <van-checkbox name="TIME_LIMITED_FREE">限时免费</van-checkbox>
              <van-checkbox name="PAID">收费</van-checkbox>
              <van-checkbox name="FREE">免费</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 运营类型 -->
        <div class="filter-section">
          <div class="section-title">运营类型</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.operationType">
              <van-checkbox name="SELF_OPERATED">自营</van-checkbox>
              <van-checkbox name="NON_SELF_OPERATED">非自营</van-checkbox>
              <van-checkbox name="INTERCONNECTED">互联</van-checkbox>
              <van-checkbox name="PRIVATE">私人</van-checkbox>
              <van-checkbox name="COOPERATION">合作</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 电站状态 -->
        <div class="filter-section">
          <div class="section-title">电站状态</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.status">
              <van-checkbox name="OPEN">营业中</van-checkbox>
              <van-checkbox name="CLOSED">停业中</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 充电方式 -->
        <div class="filter-section">
          <div class="section-title">充电方式</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.chargingMethods">
              <van-checkbox name="DC_FAST">直流快充</van-checkbox>
              <van-checkbox name="DC_SLOW">直流慢充</van-checkbox>
              <van-checkbox name="SUPER_FAST">超级快充</van-checkbox>
              <van-checkbox name="AC_FAST">交流快充</van-checkbox>
              <van-checkbox name="AC_SLOW">交流慢充</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 电站类型 -->
        <div class="filter-section">
          <div class="section-title">电站类型</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.stationType">
              <van-checkbox name="public">对外开放</van-checkbox>
              <van-checkbox name="private">不对外开放</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 电站服务 -->
        <div class="filter-section">
          <div class="section-title">电站服务</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.services">
              <van-checkbox name="rest-room">休息室</van-checkbox>
              <van-checkbox name="convenience-store">便利店</van-checkbox>
              <van-checkbox name="car-wash">洗车</van-checkbox>
              <van-checkbox name="lighting">场站照明</van-checkbox>
              <van-checkbox name="wifi">免费WIFI</van-checkbox>
              <van-checkbox name="restaurant">简餐</van-checkbox>
              <van-checkbox name="toilet">洗手间</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>

        <!-- 权益 -->
        <div class="filter-section">
          <div class="section-title">权益</div>
          <div class="filter-options">
            <van-checkbox-group v-model="filters.benefits">
              <van-checkbox name="TELD">特来电</van-checkbox>
              <van-checkbox name="PLUG_AND_CHARGE">即插即充</van-checkbox>
              <van-checkbox name="V2G">V2G</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>
      </div>
    </content-view>

    <!-- 底部按钮 -->
    <div class="filter-footer">
      <van-button
        class="reset-btn"
        size="large"
        @click="reset"
        :disabled="totalFilterCount === 0"
      >
        重置
      </van-button>
      <van-button
        class="confirm-btn"
        type="primary"
        size="large"
        @click="confirm"
      >
        确定{{ totalFilterCount > 0 ? ` (${totalFilterCount})` : '' }}
      </van-button>
    </div>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { selectFilter } from '@/bus';
import { CheckboxGroup, Checkbox, Button } from 'vant';

export default {
  name: 'ChargingStationFilter',
  components: {
    [CheckboxGroup.name]: CheckboxGroup,
    [Checkbox.name]: Checkbox,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      filters: {
        voltage: [],
        power: [],
        businessHoursType: [],
        highwayMode: [],
        parkingTypes: [],
        parkingFeeType: [],
        operationType: [],
        status: [],
        chargingMethods: [],
        stationType: [],
        services: [],
        benefits: [],
      },
    };
  },
  computed: {
    // 计算总的筛选条件数量
    totalFilterCount() {
      let count = 0;
      Object.values(this.filters).forEach(filterArray => {
        count += filterArray.length;
      });
      return count;
    },
  },
  created() {
    this.initFilters();
  },
  beforeDestroy() {
    // {{ AURA-X: Add - 组件销毁时清理缓存. Confirmed via 寸止 }}
    // 清理缓存，防止内存泄漏
    localStorage.removeItem('charging_station_filters');
  },
  methods: {
    // 初始化筛选条件
    initFilters() {
      // {{ AURA-X: Modify - 从localStorage恢复筛选状态. Confirmed via 寸止 }}
      try {
        const cachedFilters = localStorage.getItem('charging_station_filters');
        if (cachedFilters) {
          const parsedFilters = JSON.parse(cachedFilters);
          // 将数组格式的筛选条件转换为对象格式
          parsedFilters.forEach(filter => {
            if (this.filters[filter.type]) {
              this.filters[filter.type] = filter.values || [];
            }
          });
        }
      } catch (e) {
        console.error('恢复筛选条件失败:', e);
      }
    },

    // 重置筛选条件
    reset() {
      Object.keys(this.filters).forEach(key => {
        this.filters[key] = [];
      });
    },

    // 确认筛选
    confirm() {
      // {{ AURA-X: Modify - 使用EventBus发送筛选结果，清理缓存. Confirmed via 寸止 }}
      // 将筛选条件转换为数组格式
      const filterArray = [];
      Object.keys(this.filters).forEach(type => {
        if (this.filters[type].length > 0) {
          filterArray.push({
            type,
            values: this.filters[type],
          });
        }
      });

      // 先清理缓存，再发送筛选结果
      localStorage.removeItem('charging_station_filters');

      // 通过EventBus发送筛选结果
      selectFilter(filterArray);

      // 返回列表页面，不传递路由参数
      this.$_router_back();
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.charging-station-filter {
  .filter-content {
    padding: 0 16px 80px;

    .filter-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .filter-options {
        .van-checkbox {
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .filter-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 100;
    display: flex;
    gap: 12px;

    .reset-btn {
      flex: 1;

      &:disabled {
        opacity: 0.5;
      }
    }

    .confirm-btn {
      flex: 2;
    }
  }
}
</style>
